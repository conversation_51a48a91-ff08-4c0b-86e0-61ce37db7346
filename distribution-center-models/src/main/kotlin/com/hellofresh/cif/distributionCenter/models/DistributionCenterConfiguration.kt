package com.hellofresh.cif.distributionCenter.models

import com.hellofresh.cif.models.DateRange
import java.time.DayOfWeek
import java.time.LocalDate
import java.time.LocalTime
import java.time.ZoneId
import java.time.temporal.TemporalAdjusters

private const val DAYS_TO_END_DATE = 6L

@Suppress("TooManyFunctions")
data class DistributionCenterConfiguration(
    val dcCode: String,
    val productionStart: DayOfWeek,
    val cleardown: DayOfWeek,
    val market: String,
    val zoneId: ZoneId,
    val enabled: Boolean = false,
    val hasCleardown: Boolean = true,
    val wmsType: WmsSystem,
    val poCutoffTime: LocalTime? = null,
    val brand: List<String>,
) {
    // TODO: Debatable because for GB cleardown happens at 5AM(the whole cleardown logic here might be slightly erroneous)

    // TODO: Change in next PR to check(hasCleardown) = {"Cleardown is not enabled for the DC in Market:$market"}
    fun checkCleardown() = check(hasCleardown) { "Cleardown is not enabled for the DC: $dcCode in Market:$market" }

    fun isBeforeLatestCleardown(date: LocalDate) = checkCleardown().let { date.isBefore(getLatestCleardown()) }

    fun isLatestCleardown(date: LocalDate) = checkCleardown().let { date == getLatestCleardown() }

    fun getLatestCleardown() = checkCleardown().let {
        LocalDate.now(zoneId).with(TemporalAdjusters.previousOrSame(cleardown))
    }

    fun getCleardown(date: LocalDate) = date.with(TemporalAdjusters.previousOrSame(cleardown))

    fun getProductionStartDate(date: LocalDate) = date.with(TemporalAdjusters.previousOrSame(productionStart))

    fun getLatestProductionStart(): LocalDate = getProductionStartDate(LocalDate.now(zoneId))

    fun getProductionEndDate(date: LocalDate) = getProductionStartDate(date).plusDays(DAYS_TO_END_DATE)

    fun getCurrentProductionDateRange() = getProductionDateRange(LocalDate.now(zoneId))

    fun getProductionDateRange(date: LocalDate) =
        DateRange(getProductionStartDate(date), getProductionEndDate(date))

    fun getCurrentWeek() =
        ProductionWeek(getLatestProductionStart(), productionStart)

    fun getWeek(date: LocalDate) =
        ProductionWeek(date, productionStart)

    companion object
}

enum class WmsSystem(val value: String) {
    WMS_SYSTEM_FCMS("WMS_SYSTEM_FCMS"),
    WMS_SYSTEM_WMS_LITE("WMS_SYSTEM_WMS_LITE"),
    WMS_SYSTEM_HIGH_JUMP("WMS_SYSTEM_HIGH_JUMP"),
    UNRECOGNIZED("UNRECOGNIZED"),
    WMS_SYSTEM_UNSPECIFIED("WMS_SYSTEM_UNSPECIFIED");

    override fun toString(): String = value
}
